# Talent Matching Platform - .gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# VS Code
.vscode/
*.code-workspace

# Streamlit
.streamlit/

# FastAPI
.fastapi/

# Database files
*.db
*.sqlite
*.sqlite3

# PostgreSQL
*.sql
*.dump

# Neo4j
neo4j/

# Docker
.dockerignore
Dockerfile
docker-compose.yml
docker-compose.override.yml

# Podman
.podman/

# Logs
logs/
*.log
*.out
*.err

# Temporary files
tmp/
temp/
.tmp/
.temp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.lnk

# Linux
*~

# API Keys and Secrets
.env.local
.env.development.local
.env.test.local
.env.production.local
secrets.json
config.json
credentials.json

# AI/ML Models
*.pkl
*.pickle
*.joblib
*.h5
*.hdf5
*.pb
*.onnx
models/
checkpoints/

# Data files
data/raw/
data/processed/
data/external/
*.csv
*.json
*.parquet
*.feather
*.xlsx
*.xls

# Embeddings and vectors
embeddings/
vectors/
*.npy
*.npz

# Test results
test_results.csv
test_output/
coverage_html/

# Backup files
*.bak
*.backup
*.old
*.orig

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# Node.js (if using any JS tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Frontend build files
build/
dist/
.next/
out/

# Jupyter
.jupyter/

# Conda
.conda/

# R
.Rhistory
.RData
.Ruserdata

# MATLAB
*.asv
*.m~

# LaTeX
*.aux
*.bbl
*.blg
*.fdb_latexmk
*.fls
*.log
*.synctex.gz
*.toc

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Project specific
# Add any project-specific files or directories to ignore

# Local configuration
local_config.py
local_settings.py

# Cache directories
.cache/
cache/

# Monitoring and profiling
.profile
*.prof

# Documentation builds
docs/build/
docs/_build/

# Deployment
deploy/
deployment/
k8s/secrets/

# IDE
*.sublime-project
*.sublime-workspace

# JetBrains
.idea/
*.iml
*.ipr
*.iws

# Eclipse
.project
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# Gradle
.gradle/
build/

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# SBT
.sbt/
project/target/
target/

# Scala
*.class
*.log

# Rust
target/
Cargo.lock

# Go
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work

# C/C++
*.o
*.a
*.so
*.exe
*.out

# Java
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*

# Swift
*.xcodeproj/
*.xcworkspace/
DerivedData/
*.hmap
*.ipa
*.dSYM.zip
*.dSYM

# Kotlin
*.kt.class

# Dart/Flutter
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
build/

# Unity
[Ll]ibrary/
[Tt]emp/
[Oo]bj/
[Bb]uild/
[Bb]uilds/
Assets/AssetStoreTools*

# Unreal Engine
Binaries/
DerivedDataCache/
Intermediate/
Saved/
*.VC.db
*.opensdf
*.opendb
*.sdf
*.sln
*.suo
*.xcodeproj
*.xcworkspace
