# 🎯 Talent Matching Platform

An intelligent AI-powered talent matching platform that combines Retrieval-Augmented Generation (RAG) with vector databases and knowledge graphs to match talent profiles and resumes with available projects.

## 🌟 Features

- **AI-Powered Matching**: Uses Google Gemini AI for intelligent talent-project matching
- **Vector Search**: Semantic search using embeddings for finding similar profiles
- **Knowledge Graphs**: Neo4j-powered relationship mapping between talents, skills, and projects
- **Hybrid Search**: Combines vector similarity and graph traversal for optimal results
- **Interactive Dashboard**: Streamlit-based web interface with light theme
- **Comprehensive API**: FastAPI backend with automatic documentation
- **Skill Analytics**: Radar charts and skill gap analysis
- **OKR Integration**: Career planning with Objectives and Key Results framework

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Streamlit     │    │    FastAPI      │    │   Databases     │
│   Frontend      │◄──►│    Backend      │◄──►│                 │
│                 │    │                 │    │ PostgreSQL +    │
│ - Dashboard     │    │ - REST API      │    │ pgvector        │
│ - Search UI     │    │ - AI Services   │    │                 │
│ - Matching UI   │    │ - Data Models   │    │ Neo4j           │
└─────────────────┘    └─────────────────┘    │ Knowledge Graph │
                                              └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   Google        │
                    │   Gemini AI     │
                    │                 │
                    │ - Embeddings    │
                    │ - Text Analysis │
                    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- Python 3.11+ (using homebrew python3)
- Podman (for container management)
- Task (for automation)
- Google Gemini AI API key

### 1. Environment Setup

```bash
# Clone the repository
git clone <repository-url>
cd talent-matching-platform

# Create virtual environment using homebrew python3
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Database Setup

```bash
# Start databases using Taskfile
task db:start

# Initialize databases
task db:init

# Verify databases are running
task db:status
```

### 3. Data Ingestion

```bash
# Run data ingestion pipeline
python data_ingestion/main.py
```

### 4. Start Backend API

```bash
# Start FastAPI server
uvicorn backend.app.main:app --reload --host 0.0.0.0 --port 8000
```

### 5. Start Frontend

```bash
# Start Streamlit app
streamlit run frontend/streamlit_app.py --server.port 8501
```

### 6. Run Tests

```bash
# Run comprehensive test suite
python test_platform.py
```

## 📁 Project Structure

```
talent-matching-platform/
├── backend/                    # FastAPI backend
│   └── app/
│       ├── api/               # API routes
│       │   └── routes/        # Individual route modules
│       ├── core/              # Core configuration
│       ├── models/            # Pydantic models
│       └── services/          # Business logic services
├── frontend/                  # Streamlit frontend
│   └── streamlit_app.py       # Main Streamlit application
├── data_ingestion/            # Data processing pipeline
│   ├── processors/            # Document processors
│   ├── embeddings/            # Embedding generators
│   └── database_manager.py    # Database operations
├── data/                      # Data directories
│   ├── projects/              # Project markdown files
│   ├── talent/                # Internal talent profiles
│   └── resumes/               # External candidate resumes
├── Taskfile.yml              # Task automation
├── requirements.txt           # Python dependencies
├── test_platform.py          # Comprehensive test suite
├── .gitignore                 # Git ignore rules
└── README.md                  # This file
```

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the project root:

```env
# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=talent_matching
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password

# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password

# AI Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Application Configuration
DEBUG=true
LOG_LEVEL=info
```

### API Configuration

The FastAPI backend runs on `http://localhost:8000` with:
- Interactive API docs: `http://localhost:8000/docs`
- ReDoc documentation: `http://localhost:8000/redoc`
- Health check: `http://localhost:8000/health`

### Frontend Configuration

The Streamlit frontend runs on `http://localhost:8501` with:
- Light theme enabled
- Responsive design
- Interactive charts and visualizations

## 📊 API Endpoints

### Projects
- `GET /api/v1/projects/` - List all projects
- `GET /api/v1/projects/{id}` - Get project details
- `GET /api/v1/projects/{id}/team` - Get project team
- `GET /api/v1/projects/{id}/requirements` - Get project requirements

### Talent
- `GET /api/v1/talent/` - List internal talent
- `GET /api/v1/talent/{id}` - Get talent profile
- `GET /api/v1/talent/{id}/skills` - Get talent skills breakdown
- `GET /api/v1/talent/{id}/projects` - Get talent projects
- `GET /api/v1/talent/{id}/okrs` - Get talent OKRs

### Candidates
- `GET /api/v1/candidates/` - List external candidates
- `GET /api/v1/candidates/{id}` - Get candidate profile
- `GET /api/v1/candidates/{id}/skills` - Get candidate skills
- `GET /api/v1/candidates/{id}/resume` - Get candidate resume
- `GET /api/v1/candidates/{id}/matching-score` - Get matching score

### Search
- `POST /api/v1/search/` - Vector search
- `GET /api/v1/search/semantic` - Semantic search
- `GET /api/v1/search/graph` - Graph-based search
- `GET /api/v1/search/hybrid` - Hybrid search
- `GET /api/v1/search/suggestions` - Search suggestions
- `GET /api/v1/search/analytics` - Search analytics

### Matching
- `POST /api/v1/matching/` - Match by criteria
- `GET /api/v1/matching/project/{id}` - Match for project
- `GET /api/v1/matching/talent/{id}/projects` - Match talent to projects
- `GET /api/v1/matching/skills/gap-analysis` - Skill gap analysis

## 🎨 Frontend Features

### Dashboard
- Overview metrics and statistics
- Top skills distribution charts
- Recent activity feed

### Projects View
- Project listing with filters
- Project details with team information
- Skill requirements visualization

### Talent View
- Internal talent directory
- Detailed profiles with skill radar charts
- OKR tracking and career goals

### Candidates View
- External candidate database
- Resume analysis and skill extraction
- Matching score calculations

### Search Interface
- Multi-modal search (semantic, graph, hybrid)
- Real-time suggestions
- Advanced filtering options

### Matching Engine
- Project-candidate matching
- Custom criteria matching
- Skill gap analysis with recommendations

## 🧪 Testing

The platform includes a comprehensive test suite that covers:

### API Testing
- Endpoint connectivity and response validation
- Data integrity checks
- Error handling verification
- Performance benchmarking

### Frontend Testing
- UI component functionality
- Data visualization accuracy
- User interaction flows

### Integration Testing
- Database connectivity
- AI service integration
- End-to-end workflows

Run tests with:
```bash
python test_platform.py
```

Test results are saved to `test_results.csv` for analysis.

## 🔍 Data Sources

### Projects (`data/projects/`)
- Project descriptions and requirements
- Technical stack and team size
- Timeline and budget information

### Internal Talent (`data/talent/`)
- Employee profiles and skills
- Career goals and OKR frameworks
- Performance metrics and availability

### External Candidates (`data/resumes/`)
- Resume content and experience
- Skill extraction and categorization
- Education and certification details

## 🤖 AI Integration

### Google Gemini AI
- Text embedding generation
- Semantic similarity calculations
- Content analysis and extraction

### Vector Database (PostgreSQL + pgvector)
- High-dimensional vector storage
- Similarity search optimization
- Scalable embedding operations

### Knowledge Graph (Neo4j)
- Relationship mapping between entities
- Graph traversal for complex queries
- Skill and experience connections

## 📈 Analytics and Insights

### Skill Analytics
- Skill distribution across organization
- Demand vs. supply analysis
- Trending skills identification

### Matching Analytics
- Match success rates
- Common skill gaps
- Recommendation effectiveness

### Performance Metrics
- Search response times
- Matching accuracy scores
- User engagement statistics

## 🚀 Deployment

### Development
```bash
# Start all services
task dev:start

# Stop all services
task dev:stop
```

### Production
```bash
# Build and deploy with containers
task prod:build
task prod:deploy
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run the test suite
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the API documentation at `/docs`
- Run the test suite to verify setup
- Review the logs for error details
- Open an issue for bug reports

## 🔮 Future Enhancements

- Real-time notifications for new matches
- Advanced ML models for better matching
- Integration with HR systems
- Mobile application development
- Multi-language support
- Advanced analytics dashboard
