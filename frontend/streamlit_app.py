"""
Streamlit frontend for Talent Matching Platform
"""
import streamlit as st
import requests
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from typing import Dict, List, Any
import json

# Configure Streamlit page
st.set_page_config(
    page_title="Talent Matching Platform",
    page_icon="🎯",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Apply light theme
st.markdown("""
<style>
    .main {
        background-color: #ffffff;
    }
    .stApp {
        background-color: #ffffff;
    }
    .css-1d391kg {
        background-color: #f8f9fa;
    }
    .stSelectbox > div > div {
        background-color: #ffffff;
    }
    .stTextInput > div > div > input {
        background-color: #ffffff;
    }
    .stMetric {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #e9ecef;
    }
    .skill-tag {
        background-color: #e3f2fd;
        color: #1976d2;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        margin: 0.125rem;
        display: inline-block;
        font-size: 0.875rem;
    }
    .match-score {
        font-size: 1.5rem;
        font-weight: bold;
        color: #2e7d32;
    }
</style>
""", unsafe_allow_html=True)

# API Configuration
API_BASE_URL = "http://localhost:8000/api/v1"

class APIClient:
    """API client for backend communication"""
    
    @staticmethod
    def get(endpoint: str, params: Dict = None) -> Dict:
        """Make GET request to API"""
        try:
            response = requests.get(f"{API_BASE_URL}{endpoint}", params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            st.error(f"API Error: {str(e)}")
            return {}
    
    @staticmethod
    def post(endpoint: str, data: Dict = None) -> Dict:
        """Make POST request to API"""
        try:
            response = requests.post(f"{API_BASE_URL}{endpoint}", json=data)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            st.error(f"API Error: {str(e)}")
            return {}

def render_skill_tags(skills: List[str]) -> str:
    """Render skills as HTML tags"""
    if not skills:
        return "No skills listed"
    
    tags_html = ""
    for skill in skills[:10]:  # Limit to 10 skills for display
        tags_html += f'<span class="skill-tag">{skill}</span> '
    
    if len(skills) > 10:
        tags_html += f'<span class="skill-tag">+{len(skills) - 10} more</span>'
    
    return tags_html

def create_skill_radar_chart(skills_data: Dict[str, float], title: str = "Skills Radar"):
    """Create radar chart for skills"""
    categories = list(skills_data.keys())
    values = list(skills_data.values())
    
    fig = go.Figure()
    
    fig.add_trace(go.Scatterpolar(
        r=values,
        theta=categories,
        fill='toself',
        name='Skills',
        line_color='#1976d2',
        fillcolor='rgba(25, 118, 210, 0.2)'
    ))
    
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 1],
                tickmode='linear',
                tick0=0,
                dtick=0.2
            )
        ),
        showlegend=False,
        title=title,
        title_x=0.5,
        height=400,
        paper_bgcolor='white',
        plot_bgcolor='white'
    )
    
    return fig

def main():
    """Main Streamlit application"""
    
    # Sidebar navigation
    st.sidebar.title("🎯 Talent Matching Platform")
    
    page = st.sidebar.selectbox(
        "Navigate to:",
        ["Dashboard", "Projects", "Internal Talent", "External Candidates", "Search", "Matching"]
    )
    
    # Main content based on selected page
    if page == "Dashboard":
        render_dashboard()
    elif page == "Projects":
        render_projects()
    elif page == "Internal Talent":
        render_talent()
    elif page == "External Candidates":
        render_candidates()
    elif page == "Search":
        render_search()
    elif page == "Matching":
        render_matching()

def render_dashboard():
    """Render dashboard page"""
    st.title("📊 Dashboard")
    st.markdown("Welcome to the Talent Matching Platform")
    
    # Get analytics data
    analytics = APIClient.get("/search/analytics")
    
    if analytics:
        # Metrics row
        col1, col2, col3, col4 = st.columns(4)
        
        doc_counts = analytics.get('document_counts', {})
        
        with col1:
            st.metric("Total Projects", doc_counts.get('projects', 0))
        
        with col2:
            st.metric("Internal Talent", doc_counts.get('talents', 0))
        
        with col3:
            st.metric("External Candidates", doc_counts.get('candidates', 0))
        
        with col4:
            st.metric("Total Documents", doc_counts.get('total', 0))
        
        # Skills distribution chart
        st.subheader("📈 Top Skills Distribution")
        
        skill_stats = analytics.get('skill_statistics', {})
        top_skills = skill_stats.get('top_skills', [])
        
        if top_skills:
            skills_df = pd.DataFrame(top_skills)
            
            fig = px.bar(
                skills_df, 
                x='skill', 
                y='count',
                title="Most In-Demand Skills",
                color='count',
                color_continuous_scale='Blues'
            )
            fig.update_layout(
                paper_bgcolor='white',
                plot_bgcolor='white',
                xaxis_title="Skills",
                yaxis_title="Number of People"
            )
            st.plotly_chart(fig, use_container_width=True)
    
    # Recent activity (placeholder)
    st.subheader("🔄 Recent Activity")
    st.info("Recent matching activities and system updates will appear here.")

def render_projects():
    """Render projects page"""
    st.title("📋 Projects")
    
    # Filters
    col1, col2, col3 = st.columns(3)
    
    with col1:
        status_filter = st.selectbox(
            "Filter by Status:",
            ["All", "planning", "active", "on_hold", "completed", "cancelled"]
        )
    
    with col2:
        search_term = st.text_input("Search projects:")
    
    with col3:
        st.write("")  # Spacing
    
    # Get projects
    params = {"page": 1, "size": 20}
    if status_filter != "All":
        params["status"] = status_filter
    if search_term:
        params["search"] = search_term
    
    projects_data = APIClient.get("/projects/", params)
    
    if projects_data and projects_data.get('projects'):
        projects = projects_data['projects']
        
        # Display projects
        for project in projects:
            with st.expander(f"🚀 {project['name']} ({project['status'].title()})"):
                col1, col2 = st.columns([2, 1])
                
                with col1:
                    st.write(f"**Description:** {project['description']}")
                    if project.get('required_skills'):
                        st.markdown(f"**Required Skills:** {render_skill_tags(project['required_skills'])}", unsafe_allow_html=True)
                
                with col2:
                    if st.button(f"View Details", key=f"project_{project['id']}"):
                        st.session_state.selected_project = project['id']
                        st.rerun()
    else:
        st.info("No projects found.")

def render_talent():
    """Render internal talent page"""
    st.title("👥 Internal Talent")
    
    # Filters
    col1, col2, col3 = st.columns(3)
    
    with col1:
        dept_filter = st.text_input("Filter by Department:")
    
    with col2:
        skill_filter = st.text_input("Filter by Skill:")
    
    with col3:
        search_term = st.text_input("Search talent:")
    
    # Get talent
    params = {"page": 1, "size": 20}
    if dept_filter:
        params["department"] = dept_filter
    if skill_filter:
        params["skill"] = skill_filter
    if search_term:
        params["search"] = search_term
    
    talent_data = APIClient.get("/talent/", params)
    
    if talent_data and talent_data.get('talents'):
        talents = talent_data['talents']
        
        # Display talent
        for talent in talents:
            with st.expander(f"👤 {talent['name']} - {talent['title']}"):
                col1, col2 = st.columns([2, 1])
                
                with col1:
                    st.write(f"**Department:** {talent.get('department', 'N/A')}")
                    st.write(f"**Location:** {talent.get('location', 'N/A')}")
                    if talent.get('skills'):
                        skill_names = [skill['name'] for skill in talent['skills']]
                        st.markdown(f"**Skills:** {render_skill_tags(skill_names)}", unsafe_allow_html=True)
                
                with col2:
                    if st.button(f"View Profile", key=f"talent_{talent['id']}"):
                        render_talent_detail(talent['id'])
    else:
        st.info("No talent found.")

def render_talent_detail(talent_id: str):
    """Render detailed talent profile"""
    talent_detail = APIClient.get(f"/talent/{talent_id}")
    skills_data = APIClient.get(f"/talent/{talent_id}/skills")
    
    if talent_detail:
        st.subheader(f"👤 {talent_detail['name']}")
        
        col1, col2 = st.columns([1, 1])
        
        with col1:
            st.write(f"**Title:** {talent_detail['title']}")
            st.write(f"**Department:** {talent_detail.get('department', 'N/A')}")
            st.write(f"**Location:** {talent_detail.get('location', 'N/A')}")
            st.write(f"**Employee ID:** {talent_detail['employee_id']}")
        
        with col2:
            if skills_data and skills_data.get('skills_breakdown', {}).get('skill_radar'):
                radar_data = skills_data['skills_breakdown']['skill_radar']
                fig = create_skill_radar_chart(radar_data, f"Skills Profile - {talent_detail['name']}")
                st.plotly_chart(fig, use_container_width=True)

def render_candidates():
    """Render external candidates page"""
    st.title("🎯 External Candidates")
    
    # Similar structure to talent but for candidates
    # Implementation follows same pattern as render_talent()
    
    # Filters
    col1, col2, col3 = st.columns(3)
    
    with col1:
        skill_filter = st.text_input("Filter by Skill:")
    
    with col2:
        exp_filter = st.number_input("Min Years Experience:", min_value=0, value=0)
    
    with col3:
        search_term = st.text_input("Search candidates:")
    
    # Get candidates
    params = {"page": 1, "size": 20}
    if skill_filter:
        params["skill"] = skill_filter
    if exp_filter > 0:
        params["experience_min"] = exp_filter
    if search_term:
        params["search"] = search_term
    
    candidates_data = APIClient.get("/candidates/", params)
    
    if candidates_data and candidates_data.get('candidates'):
        candidates = candidates_data['candidates']
        
        # Display candidates
        for candidate in candidates:
            with st.expander(f"🎯 {candidate['name']} - {candidate['title']}"):
                col1, col2 = st.columns([2, 1])
                
                with col1:
                    st.write(f"**Experience:** {candidate.get('experience_years', 0)} years")
                    if candidate.get('skills'):
                        skill_names = [skill['name'] for skill in candidate['skills']]
                        st.markdown(f"**Skills:** {render_skill_tags(skill_names)}", unsafe_allow_html=True)
                
                with col2:
                    if st.button(f"View Resume", key=f"candidate_{candidate['id']}"):
                        render_candidate_detail(candidate['id'])
    else:
        st.info("No candidates found.")

def render_candidate_detail(candidate_id: str):
    """Render detailed candidate profile"""
    candidate_detail = APIClient.get(f"/candidates/{candidate_id}")
    skills_data = APIClient.get(f"/candidates/{candidate_id}/skills")
    
    if candidate_detail:
        st.subheader(f"🎯 {candidate_detail['name']}")
        
        col1, col2 = st.columns([1, 1])
        
        with col1:
            st.write(f"**Title:** {candidate_detail['title']}")
            st.write(f"**Experience:** {candidate_detail.get('experience_years', 0)} years")
            if candidate_detail.get('education'):
                st.write(f"**Education:** {', '.join(candidate_detail['education'])}")
        
        with col2:
            if skills_data and skills_data.get('skills_breakdown', {}).get('skill_radar'):
                radar_data = skills_data['skills_breakdown']['skill_radar']
                fig = create_skill_radar_chart(radar_data, f"Skills Profile - {candidate_detail['name']}")
                st.plotly_chart(fig, use_container_width=True)

def render_search():
    """Render search page"""
    st.title("🔍 Search")

    # Search interface
    search_query = st.text_input("Enter your search query:", placeholder="e.g., Python developer with machine learning experience")

    col1, col2, col3 = st.columns(3)

    with col1:
        search_type = st.selectbox("Search Type:", ["Semantic", "Hybrid", "Graph"])

    with col2:
        doc_type = st.selectbox("Document Type:", ["All", "project", "talent", "resume"])

    with col3:
        limit = st.number_input("Max Results:", min_value=1, max_value=50, value=10)

    if st.button("🔍 Search") and search_query:
        with st.spinner("Searching..."):
            # Prepare search parameters
            if search_type == "Semantic":
                params = {
                    "query": search_query,
                    "limit": limit
                }
                if doc_type != "All":
                    params["document_type"] = doc_type

                results = APIClient.get("/search/semantic", params)

            elif search_type == "Hybrid":
                params = {
                    "query": search_query,
                    "limit": limit
                }
                if doc_type != "All":
                    params["document_type"] = doc_type

                results = APIClient.get("/search/hybrid", params)

            else:  # Graph search
                # For graph search, extract skills from query
                skills = [skill.strip() for skill in search_query.split(",")]
                params = {
                    "skills": skills,
                    "limit": limit
                }
                results = APIClient.get("/search/graph", params)

            # Display results
            if results and results.get('results'):
                st.success(f"Found {len(results['results'])} results in {results.get('search_time_ms', 0):.2f}ms")

                for i, result in enumerate(results['results']):
                    with st.expander(f"Result {i+1}: {result.get('source_file', 'Unknown')}"):
                        if 'similarity_score' in result:
                            st.write(f"**Similarity Score:** {result['similarity_score']:.3f}")

                        st.write(f"**Content Preview:**")
                        content = result.get('content', '')
                        st.write(content[:300] + "..." if len(content) > 300 else content)

                        if result.get('metadata'):
                            st.write(f"**Metadata:**")
                            metadata = result['metadata']
                            if metadata.get('name'):
                                st.write(f"- Name: {metadata['name']}")
                            if metadata.get('skills'):
                                st.markdown(f"- Skills: {render_skill_tags(metadata['skills'])}", unsafe_allow_html=True)
            else:
                st.info("No results found.")

def render_matching():
    """Render matching page"""
    st.title("🎯 Talent Matching")

    # Matching interface
    st.subheader("Find Best Matches")

    tab1, tab2, tab3 = st.tabs(["Match for Project", "Match by Criteria", "Skill Gap Analysis"])

    with tab1:
        st.write("**Find candidates for a specific project**")

        # Get projects for selection
        projects_data = APIClient.get("/projects/", {"page": 1, "size": 100})

        if projects_data and projects_data.get('projects'):
            project_options = {p['name']: p['id'] for p in projects_data['projects']}
            selected_project_name = st.selectbox("Select Project:", list(project_options.keys()))

            col1, col2 = st.columns(2)

            with col1:
                candidate_type = st.selectbox("Candidate Type:", ["external", "internal", "all"])

            with col2:
                match_limit = st.number_input("Max Matches:", min_value=1, max_value=20, value=10)

            if st.button("Find Matches for Project"):
                project_id = project_options[selected_project_name]

                with st.spinner("Finding matches..."):
                    matches = APIClient.get(f"/matching/project/{project_id}", {
                        "candidate_type": candidate_type,
                        "limit": match_limit
                    })

                    if matches and matches.get('matches'):
                        st.success(f"Found {len(matches['matches'])} matches for {selected_project_name}")

                        for i, match in enumerate(matches['matches']):
                            person = match['person']
                            score = match['match_score']

                            with st.expander(f"Match {i+1}: {person['name']} - {score['overall_score']:.1%} match"):
                                col1, col2 = st.columns([2, 1])

                                with col1:
                                    st.write(f"**Name:** {person['name']}")
                                    st.write(f"**Title:** {person['title']}")
                                    st.write(f"**Type:** {person['type'].title()}")

                                    if match.get('matching_skills'):
                                        st.markdown(f"**Matching Skills:** {render_skill_tags(match['matching_skills'])}", unsafe_allow_html=True)

                                    if match.get('missing_skills'):
                                        st.markdown(f"**Missing Skills:** {render_skill_tags(match['missing_skills'])}", unsafe_allow_html=True)

                                with col2:
                                    st.markdown(f"<div class='match-score'>{score['overall_score']:.1%}</div>", unsafe_allow_html=True)
                                    st.write(f"Skill Match: {score['skill_match']:.1%}")
                                    st.write(f"Experience: {score['experience_match']:.1%}")
                                    st.write(f"Availability: {score['availability_match']:.1%}")
                    else:
                        st.info("No matches found for this project.")

    with tab2:
        st.write("**Define custom matching criteria**")

        required_skills = st.text_area("Required Skills (comma-separated):", placeholder="Python, Machine Learning, AWS")
        preferred_skills = st.text_area("Preferred Skills (comma-separated):", placeholder="Docker, Kubernetes")

        col1, col2 = st.columns(2)

        with col1:
            experience_level = st.selectbox("Experience Level:", ["beginner", "intermediate", "advanced", "expert"])

        with col2:
            match_limit = st.number_input("Max Matches:", min_value=1, max_value=20, value=10, key="criteria_limit")

        if st.button("Find Matches by Criteria"):
            if required_skills:
                criteria = {
                    "required_skills": [skill.strip() for skill in required_skills.split(",")],
                    "preferred_skills": [skill.strip() for skill in preferred_skills.split(",")] if preferred_skills else [],
                    "experience_level": experience_level
                }

                with st.spinner("Finding matches..."):
                    matches = APIClient.post("/matching/", criteria)

                    if matches and matches.get('matches'):
                        st.success(f"Found {len(matches['matches'])} matches")

                        for i, match in enumerate(matches['matches']):
                            person = match['person']
                            score = match['match_score']

                            with st.expander(f"Match {i+1}: {person['name']} - {score['overall_score']:.1%} match"):
                                col1, col2 = st.columns([2, 1])

                                with col1:
                                    st.write(f"**Name:** {person['name']}")
                                    st.write(f"**Title:** {person['title']}")
                                    st.write(f"**Type:** {person['type'].title()}")

                                    if match.get('recommendations'):
                                        st.write("**Recommendations:**")
                                        for rec in match['recommendations']:
                                            st.write(f"- {rec}")

                                with col2:
                                    st.markdown(f"<div class='match-score'>{score['overall_score']:.1%}</div>", unsafe_allow_html=True)
                                    st.write(f"Skill Match: {score['skill_match']:.1%}")
                                    st.write(f"Experience: {score['experience_match']:.1%}")
                    else:
                        st.info("No matches found for these criteria.")
            else:
                st.warning("Please enter at least one required skill.")

    with tab3:
        st.write("**Analyze skill gaps in your organization**")

        # Get projects for gap analysis
        if projects_data and projects_data.get('projects'):
            project_options_gap = {"Organization-wide": None}
            project_options_gap.update({p['name']: p['id'] for p in projects_data['projects']})

            selected_analysis = st.selectbox("Analysis Scope:", list(project_options_gap.keys()))

            if st.button("Analyze Skill Gaps"):
                params = {}
                if project_options_gap[selected_analysis]:
                    params["project_id"] = project_options_gap[selected_analysis]

                with st.spinner("Analyzing skill gaps..."):
                    gap_analysis = APIClient.get("/matching/skills/gap-analysis", params)

                    if gap_analysis:
                        st.subheader(f"Skill Gap Analysis - {selected_analysis}")

                        # Summary metrics
                        col1, col2, col3 = st.columns(3)

                        with col1:
                            st.metric("Total Talents", gap_analysis.get('total_talents', 0))

                        with col2:
                            critical_gaps = len(gap_analysis.get('critical_gaps', []))
                            st.metric("Critical Gaps", critical_gaps)

                        with col3:
                            total_gaps = len(gap_analysis.get('skill_gaps', []))
                            st.metric("Total Skills Analyzed", total_gaps)

                        # Skill gaps table
                        if gap_analysis.get('skill_gaps'):
                            st.subheader("Skill Gap Details")

                            gaps_df = pd.DataFrame(gap_analysis['skill_gaps'])

                            # Color code by severity
                            def color_severity(val):
                                if val == 'high':
                                    return 'background-color: #ffebee'
                                elif val == 'medium':
                                    return 'background-color: #fff3e0'
                                else:
                                    return 'background-color: #e8f5e8'

                            styled_df = gaps_df.style.applymap(color_severity, subset=['gap_severity'])
                            st.dataframe(styled_df, use_container_width=True)

                        # Recommendations
                        if gap_analysis.get('recommendations'):
                            st.subheader("Recommendations")
                            for rec in gap_analysis['recommendations']:
                                st.write(f"• {rec}")

if __name__ == "__main__":
    main()
