"""
AI service for embeddings, search, and matching
"""
import sys
import os
from pathlib import Path
from typing import List, Dict, Any, Optional
import re

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from data_ingestion.embeddings.gemini_embeddings import GeminiEmbeddingsGenerator
from ..models.schemas import Person, MatchingCriteria, MatchScore
from .database_service import DatabaseService


class AIService:
    """Service for AI-powered operations"""
    
    def __init__(self, gemini_api_key: str, database_service: DatabaseService):
        self.embeddings_generator = GeminiEmbeddingsGenerator(gemini_api_key)
        self.database_service = database_service
    
    async def search_documents(self, query: str, document_type: Optional[str] = None, limit: int = 10):
        """Search documents using vector similarity"""
        # Generate query embedding
        query_embedding = self.embeddings_generator.generate_embedding(query)
        
        # Search in PostgreSQL
        results = await self.database_service.postgres_manager.search_similar_documents(
            query_embedding=query_embedding,
            embedding_type="local",
            limit=limit,
            document_type=document_type
        )
        
        return results
    
    def extract_highlights(self, content: str, query: str, max_highlights: int = 3) -> List[str]:
        """Extract relevant highlights from content based on query"""
        highlights = []
        
        # Split content into sentences
        sentences = re.split(r'[.!?]+', content)
        
        # Find sentences containing query terms
        query_terms = query.lower().split()
        
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) < 20:  # Skip very short sentences
                continue
                
            sentence_lower = sentence.lower()
            
            # Check if sentence contains any query terms
            if any(term in sentence_lower for term in query_terms):
                highlights.append(sentence)
                
                if len(highlights) >= max_highlights:
                    break
        
        return highlights
    
    def calculate_match_score(self, person: Person, criteria: MatchingCriteria) -> MatchScore:
        """Calculate matching score between person and criteria"""
        
        # Get person skills as set
        person_skills = set(skill.name.lower() for skill in person.skills)
        required_skills = set(skill.lower() for skill in criteria.required_skills)
        preferred_skills = set(skill.lower() for skill in (criteria.preferred_skills or []))
        
        # Calculate skill match
        required_matches = len(person_skills.intersection(required_skills))
        required_total = len(required_skills)
        skill_match = required_matches / required_total if required_total > 0 else 0
        
        # Bonus for preferred skills
        preferred_matches = len(person_skills.intersection(preferred_skills))
        preferred_total = len(preferred_skills)
        preferred_bonus = (preferred_matches / preferred_total * 0.2) if preferred_total > 0 else 0
        
        # Adjust skill match with preferred bonus
        skill_match = min(1.0, skill_match + preferred_bonus)
        
        # Calculate experience match (simplified)
        experience_match = 0.8  # Default good experience match
        
        # Calculate availability match (simplified)
        availability_match = 0.9  # Default high availability
        
        # Calculate overall score
        overall_score = (
            skill_match * 0.6 +
            experience_match * 0.25 +
            availability_match * 0.15
        )
        
        return MatchScore(
            overall_score=round(overall_score, 3),
            skill_match=round(skill_match, 3),
            experience_match=round(experience_match, 3),
            availability_match=round(availability_match, 3),
            cultural_fit=0.8  # Default cultural fit score
        )
    
    def generate_recommendations(self, person: Person, criteria: MatchingCriteria) -> List[str]:
        """Generate recommendations for improving match"""
        recommendations = []
        
        person_skills = set(skill.name.lower() for skill in person.skills)
        required_skills = set(skill.lower() for skill in criteria.required_skills)
        
        missing_skills = required_skills - person_skills
        
        if missing_skills:
            recommendations.append(f"Consider training in: {', '.join(list(missing_skills)[:3])}")
        
        if len(person_skills.intersection(required_skills)) / len(required_skills) > 0.8:
            recommendations.append("Excellent skill match - highly recommended")
        
        if person.type == "internal":
            recommendations.append("Internal candidate - faster onboarding")
        else:
            recommendations.append("External candidate - fresh perspective")
        
        return recommendations
    
    def combine_search_results(self, vector_results: List[Dict], graph_results: List[Dict]) -> List[Dict]:
        """Combine vector and graph search results"""
        combined = []
        
        # Add vector results with source
        for result in vector_results:
            result['search_source'] = 'vector'
            result['combined_score'] = result['similarity_score']
            combined.append(result)
        
        # Add graph results with source
        for result in graph_results:
            result['search_source'] = 'graph'
            result['combined_score'] = result.get('skill_count', 0) / 10.0  # Normalize skill count
            combined.append(result)
        
        # Sort by combined score
        combined.sort(key=lambda x: x['combined_score'], reverse=True)
        
        return combined
    
    async def generate_ai_insights(self, query: str, results: List[Dict]) -> Dict[str, Any]:
        """Generate AI insights about search results"""
        insights = {
            "summary": f"Found {len(results)} results for '{query}'",
            "top_skills": [],
            "recommendations": [],
            "trends": []
        }
        
        # Extract skills from results
        all_skills = []
        for result in results:
            metadata = result.get('metadata', {})
            all_skills.extend(metadata.get('skills', []))
        
        # Count skill frequency
        skill_counts = {}
        for skill in all_skills:
            skill_counts[skill] = skill_counts.get(skill, 0) + 1
        
        # Get top skills
        top_skills = sorted(skill_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        insights['top_skills'] = [{'skill': skill, 'count': count} for skill, count in top_skills]
        
        # Generate recommendations
        if top_skills:
            insights['recommendations'].append(f"Focus on candidates with {top_skills[0][0]} skills")
        
        insights['recommendations'].append("Consider hybrid search for better results")
        
        return insights
