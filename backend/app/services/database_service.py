"""
Database service for managing PostgreSQL and Neo4j connections
"""
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from data_ingestion.database_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Neo4jManager
from ..core.config import settings


class DatabaseService:
    """Service for managing database connections and operations"""
    
    def __init__(self):
        self.postgres_manager = PostgreSQLManager(
            host=settings.POSTGRES_HOST,
            port=settings.POSTGRES_PORT,
            database=settings.POSTGRES_DB,
            user=settings.POSTGRES_USER,
            password=settings.POSTGRES_PASSWORD
        )
        
        self.neo4j_manager = Neo4jManager(
            uri=settings.NEO4J_URI,
            user=settings.NEO4J_USER,
            password=settings.NEO4J_PASSWORD
        )
    
    async def initialize(self):
        """Initialize both database connections"""
        await self.postgres_manager.initialize()
        self.neo4j_manager.initialize()
    
    async def close(self):
        """Close all database connections"""
        await self.postgres_manager.close()
        self.neo4j_manager.close()
    
    async def health_check(self):
        """Check health of both databases"""
        health_status = {
            "postgres": False,
            "neo4j": False
        }
        
        try:
            # Test PostgreSQL
            async with self.postgres_manager.connection_pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
            health_status["postgres"] = True
        except Exception as e:
            print(f"PostgreSQL health check failed: {e}")
        
        try:
            # Test Neo4j
            with self.neo4j_manager.driver.session() as session:
                result = session.run("RETURN 1 as test")
                result.single()
            health_status["neo4j"] = True
        except Exception as e:
            print(f"Neo4j health check failed: {e}")
        
        return health_status
