#!/usr/bin/env python3
"""
Comprehensive test script for Talent Matching Platform
Tests both backend API and frontend functionality
"""
import requests
import json
import time
import sys
import subprocess
import threading
from typing import Dict, List, Any
import pandas as pd

# Configuration
API_BASE_URL = "http://localhost:8000"
STREAMLIT_URL = "http://localhost:8501"

class Colors:
    """ANSI color codes for terminal output"""
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

class TestRunner:
    """Test runner for the Talent Matching Platform"""
    
    def __init__(self):
        self.passed_tests = 0
        self.failed_tests = 0
        self.test_results = []
    
    def log(self, message: str, color: str = Colors.WHITE):
        """Log a message with color"""
        print(f"{color}{message}{Colors.END}")
    
    def test_assert(self, condition: bool, test_name: str, details: str = ""):
        """Assert a test condition"""
        if condition:
            self.passed_tests += 1
            self.log(f"✅ PASS: {test_name}", Colors.GREEN)
            self.test_results.append({"test": test_name, "status": "PASS", "details": details})
        else:
            self.failed_tests += 1
            self.log(f"❌ FAIL: {test_name} - {details}", Colors.RED)
            self.test_results.append({"test": test_name, "status": "FAIL", "details": details})
    
    def test_api_endpoint(self, method: str, endpoint: str, expected_status: int = 200, 
                         data: Dict = None, params: Dict = None) -> Dict:
        """Test an API endpoint"""
        try:
            url = f"{API_BASE_URL}{endpoint}"
            
            if method.upper() == "GET":
                response = requests.get(url, params=params, timeout=10)
            elif method.upper() == "POST":
                response = requests.post(url, json=data, timeout=10)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            self.test_assert(
                response.status_code == expected_status,
                f"{method} {endpoint}",
                f"Expected {expected_status}, got {response.status_code}"
            )
            
            return response.json() if response.content else {}
            
        except requests.exceptions.RequestException as e:
            self.test_assert(False, f"{method} {endpoint}", f"Request failed: {str(e)}")
            return {}
    
    def run_all_tests(self):
        """Run all test suites"""
        self.log(f"\n{Colors.BOLD}🧪 Starting Talent Matching Platform Tests{Colors.END}")
        self.log("=" * 60)
        
        # Test suites
        self.test_basic_connectivity()
        self.test_health_endpoints()
        self.test_projects_api()
        self.test_talent_api()
        self.test_candidates_api()
        self.test_search_api()
        self.test_matching_api()
        self.test_data_integrity()
        
        # Summary
        self.print_summary()
    
    def test_basic_connectivity(self):
        """Test basic API connectivity"""
        self.log(f"\n{Colors.BLUE}📡 Testing Basic Connectivity{Colors.END}")
        
        # Test root endpoint
        self.test_api_endpoint("GET", "/")
        
        # Test API documentation
        try:
            response = requests.get(f"{API_BASE_URL}/docs", timeout=5)
            self.test_assert(
                response.status_code == 200,
                "API Documentation (/docs)",
                "Swagger UI should be accessible"
            )
        except Exception as e:
            self.test_assert(False, "API Documentation (/docs)", str(e))
    
    def test_health_endpoints(self):
        """Test health check endpoints"""
        self.log(f"\n{Colors.BLUE}🏥 Testing Health Endpoints{Colors.END}")
        
        health_data = self.test_api_endpoint("GET", "/health")
        
        if health_data:
            self.test_assert(
                health_data.get("status") == "healthy",
                "Health Status",
                f"Status: {health_data.get('status')}"
            )
    
    def test_projects_api(self):
        """Test projects API endpoints"""
        self.log(f"\n{Colors.BLUE}📋 Testing Projects API{Colors.END}")
        
        # Test get all projects
        projects_data = self.test_api_endpoint("GET", "/api/v1/projects/")
        
        if projects_data:
            self.test_assert(
                "projects" in projects_data,
                "Projects List Structure",
                "Response should contain 'projects' key"
            )
            
            projects = projects_data.get("projects", [])
            if projects:
                # Test get specific project
                first_project = projects[0]
                project_id = first_project.get("id")
                
                if project_id:
                    self.test_api_endpoint("GET", f"/api/v1/projects/{project_id}")
                    
                    # Test project team endpoint
                    self.test_api_endpoint("GET", f"/api/v1/projects/{project_id}/team")
                    
                    # Test project requirements endpoint
                    self.test_api_endpoint("GET", f"/api/v1/projects/{project_id}/requirements")
        
        # Test projects with filters
        self.test_api_endpoint("GET", "/api/v1/projects/", params={"status": "active"})
        self.test_api_endpoint("GET", "/api/v1/projects/", params={"search": "AI"})
    
    def test_talent_api(self):
        """Test talent API endpoints"""
        self.log(f"\n{Colors.BLUE}👥 Testing Talent API{Colors.END}")
        
        # Test get all talent
        talent_data = self.test_api_endpoint("GET", "/api/v1/talent/")
        
        if talent_data:
            self.test_assert(
                "talents" in talent_data,
                "Talent List Structure",
                "Response should contain 'talents' key"
            )
            
            talents = talent_data.get("talents", [])
            if talents:
                # Test get specific talent
                first_talent = talents[0]
                talent_id = first_talent.get("id")
                
                if talent_id:
                    self.test_api_endpoint("GET", f"/api/v1/talent/{talent_id}")
                    
                    # Test talent skills endpoint
                    self.test_api_endpoint("GET", f"/api/v1/talent/{talent_id}/skills")
                    
                    # Test talent projects endpoint
                    self.test_api_endpoint("GET", f"/api/v1/talent/{talent_id}/projects")
                    
                    # Test talent OKRs endpoint
                    self.test_api_endpoint("GET", f"/api/v1/talent/{talent_id}/okrs")
        
        # Test talent with filters
        self.test_api_endpoint("GET", "/api/v1/talent/", params={"skill": "Python"})
        self.test_api_endpoint("GET", "/api/v1/talent/", params={"department": "Engineering"})
    
    def test_candidates_api(self):
        """Test candidates API endpoints"""
        self.log(f"\n{Colors.BLUE}🎯 Testing Candidates API{Colors.END}")
        
        # Test get all candidates
        candidates_data = self.test_api_endpoint("GET", "/api/v1/candidates/")
        
        if candidates_data:
            self.test_assert(
                "candidates" in candidates_data,
                "Candidates List Structure",
                "Response should contain 'candidates' key"
            )
            
            candidates = candidates_data.get("candidates", [])
            if candidates:
                # Test get specific candidate
                first_candidate = candidates[0]
                candidate_id = first_candidate.get("id")
                
                if candidate_id:
                    self.test_api_endpoint("GET", f"/api/v1/candidates/{candidate_id}")
                    
                    # Test candidate skills endpoint
                    self.test_api_endpoint("GET", f"/api/v1/candidates/{candidate_id}/skills")
                    
                    # Test candidate resume endpoint
                    self.test_api_endpoint("GET", f"/api/v1/candidates/{candidate_id}/resume")
                    
                    # Test candidate matching score endpoint
                    self.test_api_endpoint("GET", f"/api/v1/candidates/{candidate_id}/matching-score")
        
        # Test candidates with filters
        self.test_api_endpoint("GET", "/api/v1/candidates/", params={"skill": "JavaScript"})
        self.test_api_endpoint("GET", "/api/v1/candidates/", params={"experience_min": 3})
    
    def test_search_api(self):
        """Test search API endpoints"""
        self.log(f"\n{Colors.BLUE}🔍 Testing Search API{Colors.END}")
        
        # Test semantic search
        self.test_api_endpoint("GET", "/api/v1/search/semantic", params={
            "query": "Python developer",
            "limit": 5
        })
        
        # Test graph search
        self.test_api_endpoint("GET", "/api/v1/search/graph", params={
            "skills": ["Python", "Machine Learning"],
            "limit": 5
        })
        
        # Test hybrid search
        self.test_api_endpoint("GET", "/api/v1/search/hybrid", params={
            "query": "AI engineer",
            "skills": ["Python", "TensorFlow"],
            "limit": 5
        })
        
        # Test search suggestions
        self.test_api_endpoint("GET", "/api/v1/search/suggestions", params={
            "query": "Py",
            "limit": 5
        })
        
        # Test search analytics
        analytics_data = self.test_api_endpoint("GET", "/api/v1/search/analytics")
        
        if analytics_data:
            self.test_assert(
                "document_counts" in analytics_data,
                "Search Analytics Structure",
                "Response should contain document counts"
            )
        
        # Test POST search
        search_data = {
            "query": "machine learning engineer",
            "document_type": "resume",
            "limit": 10
        }
        self.test_api_endpoint("POST", "/api/v1/search/", data=search_data)
    
    def test_matching_api(self):
        """Test matching API endpoints"""
        self.log(f"\n{Colors.BLUE}🎯 Testing Matching API{Colors.END}")
        
        # Test matching with criteria
        matching_criteria = {
            "required_skills": ["Python", "Machine Learning"],
            "preferred_skills": ["AWS", "Docker"],
            "experience_level": "intermediate"
        }
        matching_data = self.test_api_endpoint("POST", "/api/v1/matching/", data=matching_criteria)
        
        if matching_data:
            self.test_assert(
                "matches" in matching_data,
                "Matching Response Structure",
                "Response should contain matches"
            )
        
        # Test project matching (if projects exist)
        projects_data = self.test_api_endpoint("GET", "/api/v1/projects/")
        if projects_data and projects_data.get("projects"):
            project_id = projects_data["projects"][0]["id"]
            self.test_api_endpoint("GET", f"/api/v1/matching/project/{project_id}")
        
        # Test talent-project matching (if talents exist)
        talent_data = self.test_api_endpoint("GET", "/api/v1/talent/")
        if talent_data and talent_data.get("talents"):
            talent_id = talent_data["talents"][0]["id"]
            self.test_api_endpoint("GET", f"/api/v1/matching/talent/{talent_id}/projects")
        
        # Test skill gap analysis
        self.test_api_endpoint("GET", "/api/v1/matching/skills/gap-analysis")
    
    def test_data_integrity(self):
        """Test data integrity and consistency"""
        self.log(f"\n{Colors.BLUE}🔍 Testing Data Integrity{Colors.END}")
        
        # Get all data types
        projects_data = self.test_api_endpoint("GET", "/api/v1/projects/")
        talent_data = self.test_api_endpoint("GET", "/api/v1/talent/")
        candidates_data = self.test_api_endpoint("GET", "/api/v1/candidates/")
        
        # Check data consistency
        total_projects = len(projects_data.get("projects", [])) if projects_data else 0
        total_talents = len(talent_data.get("talents", [])) if talent_data else 0
        total_candidates = len(candidates_data.get("candidates", [])) if candidates_data else 0
        
        self.test_assert(
            total_projects > 0,
            "Projects Data Exists",
            f"Found {total_projects} projects"
        )
        
        self.test_assert(
            total_talents > 0,
            "Talent Data Exists",
            f"Found {total_talents} talents"
        )
        
        self.test_assert(
            total_candidates > 0,
            "Candidates Data Exists",
            f"Found {total_candidates} candidates"
        )
        
        # Verify analytics matches actual data
        analytics_data = self.test_api_endpoint("GET", "/api/v1/search/analytics")
        if analytics_data:
            doc_counts = analytics_data.get("document_counts", {})
            
            self.test_assert(
                doc_counts.get("projects", 0) == total_projects,
                "Analytics Projects Count",
                f"Analytics: {doc_counts.get('projects')}, Actual: {total_projects}"
            )
    
    def print_summary(self):
        """Print test summary"""
        total_tests = self.passed_tests + self.failed_tests
        pass_rate = (self.passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        self.log(f"\n{Colors.BOLD}📊 Test Summary{Colors.END}")
        self.log("=" * 60)
        self.log(f"Total Tests: {total_tests}")
        self.log(f"Passed: {self.passed_tests}", Colors.GREEN)
        self.log(f"Failed: {self.failed_tests}", Colors.RED)
        self.log(f"Pass Rate: {pass_rate:.1f}%", Colors.GREEN if pass_rate >= 80 else Colors.YELLOW)
        
        if self.failed_tests > 0:
            self.log(f"\n{Colors.RED}Failed Tests:{Colors.END}")
            for result in self.test_results:
                if result["status"] == "FAIL":
                    self.log(f"❌ {result['test']}: {result['details']}", Colors.RED)
        
        # Save results to file
        results_df = pd.DataFrame(self.test_results)
        results_df.to_csv("test_results.csv", index=False)
        self.log(f"\n📄 Test results saved to test_results.csv")

def test_streamlit_app():
    """Test Streamlit application"""
    print(f"\n{Colors.BLUE}🎨 Testing Streamlit Application{Colors.END}")
    
    try:
        response = requests.get(STREAMLIT_URL, timeout=10)
        if response.status_code == 200:
            print(f"{Colors.GREEN}✅ Streamlit app is accessible at {STREAMLIT_URL}{Colors.END}")
        else:
            print(f"{Colors.RED}❌ Streamlit app returned status {response.status_code}{Colors.END}")
    except requests.exceptions.RequestException:
        print(f"{Colors.YELLOW}⚠️  Streamlit app not accessible at {STREAMLIT_URL}{Colors.END}")
        print(f"{Colors.YELLOW}   Make sure to run: streamlit run frontend/streamlit_app.py{Colors.END}")

def main():
    """Main test function"""
    print(f"{Colors.BOLD}🧪 Talent Matching Platform Test Suite{Colors.END}")
    print(f"Testing API at: {API_BASE_URL}")
    print(f"Testing Streamlit at: {STREAMLIT_URL}")
    
    # Wait for API to be ready
    print(f"\n{Colors.YELLOW}⏳ Waiting for API to be ready...{Colors.END}")
    for i in range(30):  # Wait up to 30 seconds
        try:
            response = requests.get(f"{API_BASE_URL}/health", timeout=2)
            if response.status_code == 200:
                print(f"{Colors.GREEN}✅ API is ready!{Colors.END}")
                break
        except:
            pass
        time.sleep(1)
        print(".", end="", flush=True)
    else:
        print(f"\n{Colors.RED}❌ API not ready after 30 seconds{Colors.END}")
        print(f"{Colors.YELLOW}   Make sure to run: uvicorn backend.app.main:app --reload{Colors.END}")
        return
    
    # Run API tests
    runner = TestRunner()
    runner.run_all_tests()
    
    # Test Streamlit app
    test_streamlit_app()
    
    print(f"\n{Colors.BOLD}🎉 Testing Complete!{Colors.END}")

if __name__ == "__main__":
    main()
